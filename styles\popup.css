/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Microsoft YaHei", "SimSun", serif;
    font-size: 14px;
    line-height: 1.6;
    color: #333;
    background: #f5f7fa;
    width: 450px;        /* 增加宽度 */
    min-height: 550px;   /* 减少最小高度 */
}

#app {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-height: 650px;   /* 减少最大高度 */
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #1E3A8A, #3B82F6);
    color: white;
    padding: 16px 20px;
    text-align: center;
    position: relative;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 4px;
}

.version {
    font-size: 12px;
    opacity: 0.8;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

/* 导航标签 */
.nav-tabs {
    display: flex;
    background: white;
    border-bottom: 1px solid #e5e7eb;
}

.tab-btn {
    flex: 1;
    padding: 12px 8px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 13px;
    color: #6b7280;
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
}

.tab-btn:hover {
    background: #f9fafb;
    color: #1E3A8A;
}

.tab-btn.active {
    color: #1E3A8A;
    border-bottom-color: #1E3A8A;
    background: #f9fafb;
}

/* 标签内容 */
.tab-content {
    display: none;
    padding: 20px;
    background: white;
    min-height: 400px;
}

.tab-content.active {
    display: block;
}

/* 表单样式 */
.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #374151;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #1E3A8A;
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

/* 按钮样式 */
.btn-primary {
    background: #1E3A8A;
    color: white;
    border: none;
    padding: 10px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: background 0.3s ease;
    width: 100%;
}

.btn-primary:hover:not(:disabled) {
    background: #1e40af;
}

.btn-primary:disabled {
    background: #9ca3af;
    cursor: not-allowed;
}

.btn-secondary {
    background: #6b7280;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    margin-left: 8px;
    transition: background 0.3s ease;
}

.btn-secondary:hover {
    background: #4b5563;
}

.btn-danger {
    background: #dc2626;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    margin-left: 8px;
}

.btn-danger:hover {
    background: #b91c1c;
}

#auto-fill-btn {
    margin-top: 8px;
    width: auto;
    padding: 6px 12px;
    font-size: 12px;
}

/* 进度条样式 */
.progress-section {
    margin: 20px 0;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 12px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #1E3A8A, #3B82F6);
    width: 0%;
    transition: width 0.5s ease;
}

.progress-text {
    text-align: center;
    font-size: 13px;
    color: #6b7280;
    margin-bottom: 16px;
}

.progress-steps {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 4px;
    margin-top: 12px;
}

.step {
    text-align: center;
    font-size: 10px;
    padding: 6px 2px;
    border-radius: 4px;
    background: #f3f4f6;
    color: #6b7280;
    transition: all 0.3s ease;
}

.step.active {
    background: #1E3A8A;
    color: white;
}

.step.completed {
    background: #10b981;
    color: white;
}

/* 报告预览样式 */
.report-preview {
    margin-top: 20px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    overflow: hidden;
}

.report-preview h4 {
    background: #f9fafb;
    padding: 12px 16px;
    margin: 0;
    border-bottom: 1px solid #e5e7eb;
    font-size: 14px;
    color: #374151;
}

.report-content {
    padding: 16px;
    max-height: 300px;
    overflow-y: auto;
    font-size: 13px;
    line-height: 1.6;
}

.report-actions {
    padding: 12px 16px;
    background: #f9fafb;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 8px;
}

.report-actions .btn-primary,
.report-actions .btn-secondary {
    flex: 1;
    width: auto;
}

/* 历史记录样式 */
.search-controls {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.search-controls input {
    flex: 1;
}

.search-controls button {
    width: auto;
    padding: 8px 12px;
    font-size: 13px;
}

.report-list {
    max-height: 400px;
    overflow-y: auto;
}

.report-item {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.report-item:hover {
    border-color: #1E3A8A;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.report-item h5 {
    margin: 0 0 4px 0;
    font-size: 14px;
    color: #1f2937;
}

.report-item .meta {
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 8px;
}

.report-item .actions {
    display: flex;
    gap: 8px;
}

.report-item .actions button {
    padding: 4px 8px;
    font-size: 11px;
    border-radius: 4px;
}

.empty-state {
    text-align: center;
    color: #6b7280;
    padding: 40px 20px;
    font-size: 13px;
}

/* 底部状态栏 */
.footer {
    background: #f9fafb;
    border-top: 1px solid #e5e7eb;
    padding: 8px 20px;
    text-align: center;
}

.status {
    font-size: 12px;
    color: #6b7280;
}

/* 项目概览样式 */
.project-summary {
    background: #f0f9ff;
    border: 1px solid #0ea5e9;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 16px;
}

.project-summary h4 {
    margin: 0 0 8px 0;
    color: #0c4a6e;
    font-size: 14px;
}

.summary-content {
    font-size: 13px;
    color: #0c4a6e;
}

/* 报告章节样式 */
.report-section {
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;
}

.report-section:last-child {
    border-bottom: none;
}

.report-section h4 {
    color: #1E3A8A;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 600;
}

.report-section p {
    color: #374151;
    line-height: 1.6;
    font-size: 13px;
    margin: 0;
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 10px;
    right: 10px;
    padding: 12px 16px;
    border-radius: 6px;
    color: white;
    font-size: 13px;
    z-index: 10000;
    max-width: 300px;
    word-wrap: break-word;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.notification-success {
    background-color: #10b981;
}

.notification-error {
    background-color: #ef4444;
}

.notification-warning {
    background-color: #f59e0b;
}

.notification-info {
    background-color: #3b82f6;
}

/* 动画 */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #1E3A8A;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 响应式调整 */
@media (max-width: 380px) {
    body {
        width: 100%;
    }

    .progress-steps {
        grid-template-columns: repeat(4, 1fr);
    }

    .step {
        font-size: 9px;
        padding: 4px 1px;
    }

    .nav-tabs {
        font-size: 12px;
    }

    .tab-btn {
        padding: 10px 6px;
    }
}
