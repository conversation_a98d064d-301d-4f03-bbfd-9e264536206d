<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI区块链项目报告生成器</title>
    <link rel="stylesheet" href="styles/popup.css">
</head>
<body>
    <div id="app">
        <div class="header">
            <h1>AI区块链报告生成器</h1>
            <div class="version">v1.0.0</div>
        </div>
        
        <div class="main-content">
            <!-- 导航标签 -->
            <div class="nav-tabs">
                <button class="tab-btn active" data-tab="input">项目输入</button>
                <button class="tab-btn" data-tab="generate">生成报告</button>
                <button class="tab-btn" data-tab="history">历史记录</button>
            </div>
            
            <!-- 项目输入页面 -->
            <div class="tab-content active" id="input-tab">
                <div class="input-section">
                    <h3>项目信息输入</h3>
                    <form id="project-form">
                        <div class="form-group">
                            <label for="project-url">项目网址:</label>
                            <input type="url" id="project-url" placeholder="https://example.com">
                            <button type="button" id="auto-fill-btn">自动填充当前页面</button>
                        </div>
                        
                        <div class="form-group">
                            <label for="project-name">项目名称:</label>
                            <input type="text" id="project-name" placeholder="输入项目名称" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="contract-address">合约地址:</label>
                            <input type="text" id="contract-address" placeholder="0x...">
                        </div>
                        
                        <div class="form-group">
                            <label for="project-description">项目描述:</label>
                            <textarea id="project-description" placeholder="简要描述项目功能和特点" rows="3"></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label for="blockchain-network">区块链网络:</label>
                            <select id="blockchain-network">
                                <option value="">选择网络</option>
                                <option value="ethereum">以太坊</option>
                                <option value="bsc">币安智能链</option>
                                <option value="polygon">Polygon</option>
                                <option value="arbitrum">Arbitrum</option>
                                <option value="optimism">Optimism</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        
                        <button type="submit" class="btn-primary">验证并继续</button>
                    </form>
                </div>
            </div>
            
            <!-- 报告生成页面 -->
            <div class="tab-content" id="generate-tab">
                <div class="generate-section">
                    <h3>AI报告生成</h3>
                    <div class="project-summary" id="project-summary" style="display: none;">
                        <h4>项目概览</h4>
                        <div class="summary-content"></div>
                    </div>
                    
                    <div class="generation-controls">
                        <button id="generate-btn" class="btn-primary" disabled>生成报告</button>
                        <button id="mock-generate-btn" class="btn-secondary">使用模拟数据</button>
                    </div>
                    
                    <div class="progress-section" id="progress-section" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill" id="progress-fill"></div>
                        </div>
                        <div class="progress-text" id="progress-text">准备中...</div>
                        <div class="progress-steps">
                            <div class="step" data-step="1">项目分析</div>
                            <div class="step" data-step="2">团队调研</div>
                            <div class="step" data-step="3">技术评估</div>
                            <div class="step" data-step="4">市场分析</div>
                            <div class="step" data-step="5">风险评估</div>
                            <div class="step" data-step="6">投资建议</div>
                            <div class="step" data-step="7">报告整合</div>
                        </div>
                    </div>
                    
                    <div class="report-preview" id="report-preview" style="display: none;">
                        <h4>报告预览</h4>
                        <div class="report-content" id="report-content"></div>
                        <div class="report-actions">
                            <button id="save-report-btn" class="btn-primary">保存报告</button>
                            <button id="export-pdf-btn" class="btn-secondary">导出PDF</button>
                            <button id="export-json-btn" class="btn-secondary">导出JSON</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 历史记录页面 -->
            <div class="tab-content" id="history-tab">
                <div class="history-section">
                    <h3>历史报告</h3>
                    <div class="search-controls">
                        <input type="text" id="search-input" placeholder="搜索报告...">
                        <button id="search-btn">搜索</button>
                        <button id="clear-all-btn" class="btn-danger">清空所有</button>
                    </div>
                    <div class="report-list" id="report-list">
                        <div class="empty-state">暂无历史报告</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <div class="status" id="status">就绪</div>
        </div>
    </div>
    
    <script src="scripts/popup.js"></script>
</body>
</html>
