// Popup主要逻辑
class AIReportGenerator {
    constructor() {
        this.currentTab = 'input';
        this.currentProject = null;
        this.currentReport = null;
        this.isGenerating = false;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadSettings();
        this.updateStatus('就绪');
    }
    
    bindEvents() {
        // 标签切换
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });
        
        // 自动填充按钮
        document.getElementById('auto-fill-btn').addEventListener('click', () => {
            this.autoFillCurrentPage();
        });
        
        // 项目表单提交
        document.getElementById('project-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.validateAndProceed();
        });
        
        // 生成报告按钮
        document.getElementById('generate-btn').addEventListener('click', () => {
            this.generateReport();
        });
        
        // 模拟数据按钮
        document.getElementById('mock-generate-btn').addEventListener('click', () => {
            this.generateMockReport();
        });
        
        // 保存报告按钮
        document.getElementById('save-report-btn').addEventListener('click', () => {
            this.saveCurrentReport();
        });
        
        // 导出按钮
        document.getElementById('export-pdf-btn').addEventListener('click', () => {
            this.exportReport('pdf');
        });
        
        document.getElementById('export-json-btn').addEventListener('click', () => {
            this.exportReport('json');
        });
        
        // 历史记录相关
        document.getElementById('search-btn').addEventListener('click', () => {
            this.searchReports();
        });
        
        document.getElementById('clear-all-btn').addEventListener('click', () => {
            this.clearAllReports();
        });
        
        // 搜索框回车
        document.getElementById('search-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchReports();
            }
        });
    }
    
    // 切换标签
    switchTab(tabName) {
        // 更新按钮状态
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        
        // 更新内容区域
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');
        
        this.currentTab = tabName;
        
        // 加载对应数据
        if (tabName === 'history') {
            this.loadReportHistory();
        }
    }
    
    // 自动填充当前页面信息
    async autoFillCurrentPage() {
        try {
            this.updateStatus('正在获取页面信息...');
            
            const response = await chrome.runtime.sendMessage({ action: 'getCurrentTab' });
            
            if (response.success) {
                const data = response.data;
                
                // 填充表单
                document.getElementById('project-url').value = data.url || '';
                document.getElementById('project-name').value = data.title || '';
                document.getElementById('contract-address').value = data.contractAddress || '';
                document.getElementById('project-description').value = data.description || '';
                
                if (data.blockchain) {
                    document.getElementById('blockchain-network').value = data.blockchain;
                }
                
                this.updateStatus('页面信息已自动填充');
                
                // 显示提示
                this.showNotification('已自动填充当前页面信息', 'success');
            } else {
                throw new Error(response.error || '获取页面信息失败');
            }
        } catch (error) {
            console.error('自动填充失败:', error);
            this.updateStatus('自动填充失败');
            this.showNotification('自动填充失败: ' + error.message, 'error');
        }
    }
    
    // 验证表单并继续
    validateAndProceed() {
        const formData = this.getFormData();
        
        // 基础验证
        if (!formData.name.trim()) {
            this.showNotification('请输入项目名称', 'error');
            return;
        }
        
        // URL验证
        if (formData.url && !this.isValidUrl(formData.url)) {
            this.showNotification('请输入有效的网址', 'error');
            return;
        }
        
        // 合约地址验证
        if (formData.contractAddress && !this.isValidContractAddress(formData.contractAddress)) {
            this.showNotification('请输入有效的合约地址', 'error');
            return;
        }
        
        this.currentProject = formData;
        
        // 显示项目概览
        this.showProjectSummary(formData);
        
        // 切换到生成页面
        this.switchTab('generate');
        
        // 启用生成按钮
        document.getElementById('generate-btn').disabled = false;
        
        this.updateStatus('项目信息验证通过，可以生成报告');
        this.showNotification('项目信息验证通过', 'success');
    }
    
    // 获取表单数据
    getFormData() {
        return {
            url: document.getElementById('project-url').value.trim(),
            name: document.getElementById('project-name').value.trim(),
            contractAddress: document.getElementById('contract-address').value.trim(),
            description: document.getElementById('project-description').value.trim(),
            blockchain: document.getElementById('blockchain-network').value
        };
    }
    
    // URL验证
    isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }
    
    // 合约地址验证
    isValidContractAddress(address) {
        // 以太坊地址格式验证
        return /^0x[a-fA-F0-9]{40}$/.test(address);
    }
    
    // 显示项目概览
    showProjectSummary(project) {
        const summaryElement = document.getElementById('project-summary');
        const contentElement = summaryElement.querySelector('.summary-content');
        
        contentElement.innerHTML = `
            <p><strong>项目名称:</strong> ${project.name}</p>
            ${project.url ? `<p><strong>项目网址:</strong> ${project.url}</p>` : ''}
            ${project.contractAddress ? `<p><strong>合约地址:</strong> ${project.contractAddress}</p>` : ''}
            ${project.blockchain ? `<p><strong>区块链网络:</strong> ${this.getBlockchainName(project.blockchain)}</p>` : ''}
            ${project.description ? `<p><strong>项目描述:</strong> ${project.description}</p>` : ''}
        `;
        
        summaryElement.style.display = 'block';
    }
    
    // 获取区块链网络名称
    getBlockchainName(value) {
        const names = {
            'ethereum': '以太坊',
            'bsc': '币安智能链',
            'polygon': 'Polygon',
            'arbitrum': 'Arbitrum',
            'optimism': 'Optimism',
            'other': '其他'
        };
        return names[value] || value;
    }
    
    // 生成AI报告
    async generateReport() {
        if (this.isGenerating || !this.currentProject) {
            return;
        }
        
        this.isGenerating = true;
        this.showProgressSection();
        this.updateGenerateButtons(true);
        
        try {
            this.updateStatus('正在生成AI报告...');
            
            // 模拟生成步骤
            await this.simulateGenerationSteps();
            
            // 调用后台脚本生成报告
            const response = await chrome.runtime.sendMessage({
                action: 'generateReport',
                data: this.currentProject
            });
            
            if (response.success) {
                this.currentReport = response.data;
                this.showReportPreview(response.data);
                
                if (response.isMock) {
                    this.updateStatus('报告生成完成（使用模拟数据）');
                    this.showNotification('API调用失败，已使用模拟数据生成报告', 'warning');
                } else {
                    this.updateStatus('AI报告生成完成');
                    this.showNotification('报告生成成功', 'success');
                }
            } else {
                throw new Error(response.error || '生成报告失败');
            }
        } catch (error) {
            console.error('生成报告失败:', error);
            this.updateStatus('报告生成失败');
            this.showNotification('生成报告失败: ' + error.message, 'error');
        } finally {
            this.isGenerating = false;
            this.updateGenerateButtons(false);
        }
    }
    
    // 生成模拟报告
    async generateMockReport() {
        if (this.isGenerating || !this.currentProject) {
            return;
        }
        
        this.isGenerating = true;
        this.showProgressSection();
        this.updateGenerateButtons(true);
        
        try {
            this.updateStatus('正在生成模拟报告...');
            
            // 模拟生成步骤
            await this.simulateGenerationSteps();
            
            // 生成模拟数据
            const mockReport = this.createMockReport(this.currentProject);
            this.currentReport = mockReport;
            this.showReportPreview(mockReport);
            
            this.updateStatus('模拟报告生成完成');
            this.showNotification('模拟报告生成成功', 'success');
        } catch (error) {
            console.error('生成模拟报告失败:', error);
            this.updateStatus('模拟报告生成失败');
            this.showNotification('生成模拟报告失败: ' + error.message, 'error');
        } finally {
            this.isGenerating = false;
            this.updateGenerateButtons(false);
        }
    }
    
    // 模拟生成步骤
    async simulateGenerationSteps() {
        const steps = [
            { step: 1, text: '分析项目基础信息...', duration: 800 },
            { step: 2, text: '调研团队背景...', duration: 1000 },
            { step: 3, text: '评估技术架构...', duration: 1200 },
            { step: 4, text: '分析市场情况...', duration: 900 },
            { step: 5, text: '评估投资风险...', duration: 1100 },
            { step: 6, text: '生成投资建议...', duration: 800 },
            { step: 7, text: '整合报告内容...', duration: 600 }
        ];
        
        for (let i = 0; i < steps.length; i++) {
            const stepInfo = steps[i];
            this.updateProgress(stepInfo.step, stepInfo.text, (i + 1) / steps.length * 100);
            await this.delay(stepInfo.duration);
        }
    }
    
    // 更新进度
    updateProgress(step, text, percentage) {
        document.getElementById('progress-text').textContent = text;
        document.getElementById('progress-fill').style.width = percentage + '%';
        
        // 更新步骤状态
        document.querySelectorAll('.step').forEach((stepEl, index) => {
            stepEl.classList.remove('active', 'completed');
            if (index + 1 < step) {
                stepEl.classList.add('completed');
            } else if (index + 1 === step) {
                stepEl.classList.add('active');
            }
        });
    }
    
    // 延迟函数
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    // 显示进度区域
    showProgressSection() {
        document.getElementById('progress-section').style.display = 'block';
        document.getElementById('report-preview').style.display = 'none';
    }
    
    // 更新生成按钮状态
    updateGenerateButtons(disabled) {
        document.getElementById('generate-btn').disabled = disabled;
        document.getElementById('mock-generate-btn').disabled = disabled;
    }
    
    // 创建模拟报告
    createMockReport(project) {
        return {
            id: Date.now().toString(),
            projectName: project.name,
            projectUrl: project.url,
            contractAddress: project.contractAddress,
            blockchain: project.blockchain,
            createdAt: new Date().toISOString(),
            isMock: true,
            sections: {
                overview: `${project.name}是一个基于${this.getBlockchainName(project.blockchain) || '区块链'}技术的创新项目。该项目致力于通过去中心化的方式解决传统行业痛点，为用户提供更加透明、安全、高效的服务体验。项目团队具有丰富的区块链开发经验，技术架构设计合理，具有良好的发展前景。`,
                team: `项目团队由经验丰富的区块链开发者、产品经理和市场专家组成。核心团队成员在相关领域拥有多年从业经验，曾参与多个成功的区块链项目开发。团队具备强大的技术实力和丰富的行业资源，为项目的成功奠定了坚实基础。`,
                technology: `项目采用先进的区块链技术架构，智能合约经过严格的安全审计。技术实现方案成熟稳定，具有良好的可扩展性和安全性。合约代码开源透明，接受社区监督，确保项目的去中心化特性和用户资产安全。`,
                market: `项目所在市场具有巨大的发展潜力，目标用户群体庞大。通过对比分析主要竞争对手，该项目在技术创新、用户体验等方面具有明显优势。随着区块链技术的普及和应用场景的扩大，项目有望获得快速发展。`,
                tokenomics: `项目代币经济模型设计合理，代币分配透明公正。通过多种激励机制鼓励用户参与生态建设，形成良性循环。代币具有明确的使用场景和价值支撑，长期持有价值较高。`,
                risks: `项目面临的主要风险包括：技术风险（智能合约漏洞、网络安全等）、市场风险（竞争加剧、用户接受度等）、监管风险（政策变化、合规要求等）。建议投资者充分了解相关风险，理性投资。`,
                investment: `综合评估，该项目在技术、团队、市场等方面表现良好，具有较高的投资价值。建议关注项目进展，适度配置。投资评级：B+（推荐关注）。风险提示：区块链投资存在较高风险，请谨慎投资。`
            },
            summary: `${project.name}是一个基于区块链技术的创新项目，具有良好的发展前景和投资价值...`
        };
    }
    
    // 显示报告预览
    showReportPreview(report) {
        document.getElementById('progress-section').style.display = 'none';
        
        const previewElement = document.getElementById('report-preview');
        const contentElement = document.getElementById('report-content');
        
        let html = `<h3>${report.projectName} - 区块链项目分析报告</h3>`;
        
        if (report.isMock) {
            html += `<div style="background: #fef3c7; border: 1px solid #f59e0b; padding: 8px; border-radius: 4px; margin-bottom: 16px; font-size: 12px;">
                <strong>注意:</strong> 这是模拟数据，仅供演示使用
            </div>`;
        }
        
        // 添加各个章节
        const sectionTitles = {
            overview: '1. 项目概述',
            team: '2. 团队背景',
            technology: '3. 技术架构',
            market: '4. 市场分析',
            tokenomics: '5. 代币经济',
            risks: '6. 风险评估',
            investment: '7. 投资建议'
        };
        
        Object.entries(sectionTitles).forEach(([key, title]) => {
            if (report.sections[key]) {
                html += `
                    <div class="report-section">
                        <h4>${title}</h4>
                        <p>${report.sections[key]}</p>
                    </div>
                `;
            }
        });
        
        contentElement.innerHTML = html;
        previewElement.style.display = 'block';
    }
    
    // 保存当前报告
    async saveCurrentReport() {
        if (!this.currentReport) {
            this.showNotification('没有可保存的报告', 'error');
            return;
        }
        
        try {
            this.updateStatus('正在保存报告...');
            
            const response = await chrome.runtime.sendMessage({
                action: 'saveReport',
                data: this.currentReport
            });
            
            if (response.success) {
                this.updateStatus('报告保存成功');
                this.showNotification('报告已保存到历史记录', 'success');
            } else {
                throw new Error(response.error || '保存失败');
            }
        } catch (error) {
            console.error('保存报告失败:', error);
            this.updateStatus('保存报告失败');
            this.showNotification('保存报告失败: ' + error.message, 'error');
        }
    }
    
    // 导出报告
    exportReport(format) {
        if (!this.currentReport) {
            this.showNotification('没有可导出的报告', 'error');
            return;
        }
        
        try {
            if (format === 'json') {
                this.exportAsJSON();
            } else if (format === 'pdf') {
                this.exportAsPDF();
            }
        } catch (error) {
            console.error('导出失败:', error);
            this.showNotification('导出失败: ' + error.message, 'error');
        }
    }
    
    // 导出为JSON
    exportAsJSON() {
        const dataStr = JSON.stringify(this.currentReport, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        
        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `${this.currentReport.projectName}_report.json`;
        link.click();
        
        this.showNotification('JSON文件已下载', 'success');
    }
    
    // 导出为PDF（简化版本）
    exportAsPDF() {
        // 创建一个新窗口用于打印
        const printWindow = window.open('', '_blank');
        
        const reportContent = document.getElementById('report-content').innerHTML;
        
        printWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>${this.currentReport.projectName} - 区块链项目分析报告</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; }
                    h3 { color: #1E3A8A; }
                    h4 { color: #374151; margin-top: 20px; }
                    .report-section { margin-bottom: 20px; }
                    p { line-height: 1.6; }
                </style>
            </head>
            <body>
                ${reportContent}
                <div style="margin-top: 30px; font-size: 12px; color: #666;">
                    生成时间: ${new Date(this.currentReport.createdAt).toLocaleString()}
                </div>
            </body>
            </html>
        `);
        
        printWindow.document.close();
        printWindow.print();
        
        this.showNotification('请在打印对话框中选择"保存为PDF"', 'info');
    }
    
    // 加载报告历史
    async loadReportHistory() {
        try {
            this.updateStatus('正在加载历史记录...');
            
            const response = await chrome.runtime.sendMessage({ action: 'getReports' });
            
            if (response.success) {
                this.displayReportList(response.data);
                this.updateStatus(`已加载 ${response.data.length} 个历史报告`);
            } else {
                throw new Error(response.error || '加载失败');
            }
        } catch (error) {
            console.error('加载历史记录失败:', error);
            this.updateStatus('加载历史记录失败');
            this.showNotification('加载历史记录失败: ' + error.message, 'error');
        }
    }
    
    // 显示报告列表
    displayReportList(reports) {
        const listElement = document.getElementById('report-list');
        
        if (reports.length === 0) {
            listElement.innerHTML = '<div class="empty-state">暂无历史报告</div>';
            return;
        }
        
        const html = reports.map(report => `
            <div class="report-item" data-id="${report.id}">
                <h5>${report.projectName}</h5>
                <div class="meta">
                    ${report.blockchain ? `${this.getBlockchainName(report.blockchain)} • ` : ''}
                    ${new Date(report.createdAt).toLocaleString()}
                    ${report.isMock ? ' • 模拟数据' : ''}
                </div>
                <p style="font-size: 12px; color: #666; margin: 8px 0;">${report.summary || '暂无摘要'}</p>
                <div class="actions">
                    <button class="btn-secondary view-btn" data-id="${report.id}">查看</button>
                    <button class="btn-secondary export-btn" data-id="${report.id}">导出</button>
                    <button class="btn-danger delete-btn" data-id="${report.id}">删除</button>
                </div>
            </div>
        `).join('');
        
        listElement.innerHTML = html;
        
        // 绑定事件
        this.bindReportListEvents();
    }
    
    // 绑定报告列表事件
    bindReportListEvents() {
        // 查看报告
        document.querySelectorAll('.view-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.viewReport(e.target.dataset.id);
            });
        });
        
        // 导出报告
        document.querySelectorAll('.export-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.exportHistoryReport(e.target.dataset.id);
            });
        });
        
        // 删除报告
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.deleteReport(e.target.dataset.id);
            });
        });
    }
    
    // 查看历史报告
    async viewReport(reportId) {
        try {
            const response = await chrome.runtime.sendMessage({ action: 'getReports' });
            
            if (response.success) {
                const report = response.data.find(r => r.id === reportId);
                if (report) {
                    this.currentReport = report;
                    this.showReportPreview(report);
                    this.switchTab('generate');
                }
            }
        } catch (error) {
            console.error('查看报告失败:', error);
            this.showNotification('查看报告失败: ' + error.message, 'error');
        }
    }
    
    // 导出历史报告
    async exportHistoryReport(reportId) {
        try {
            const response = await chrome.runtime.sendMessage({ action: 'getReports' });
            
            if (response.success) {
                const report = response.data.find(r => r.id === reportId);
                if (report) {
                    this.currentReport = report;
                    this.exportAsJSON();
                }
            }
        } catch (error) {
            console.error('导出报告失败:', error);
            this.showNotification('导出报告失败: ' + error.message, 'error');
        }
    }
    
    // 删除报告
    async deleteReport(reportId) {
        if (!confirm('确定要删除这个报告吗？')) {
            return;
        }
        
        try {
            const response = await chrome.runtime.sendMessage({
                action: 'deleteReport',
                reportId: reportId
            });
            
            if (response.success) {
                this.showNotification('报告已删除', 'success');
                this.loadReportHistory(); // 重新加载列表
            } else {
                throw new Error(response.error || '删除失败');
            }
        } catch (error) {
            console.error('删除报告失败:', error);
            this.showNotification('删除报告失败: ' + error.message, 'error');
        }
    }
    
    // 搜索报告
    async searchReports() {
        const query = document.getElementById('search-input').value.trim().toLowerCase();
        
        try {
            const response = await chrome.runtime.sendMessage({ action: 'getReports' });
            
            if (response.success) {
                let filteredReports = response.data;
                
                if (query) {
                    filteredReports = response.data.filter(report => 
                        report.projectName.toLowerCase().includes(query) ||
                        (report.summary && report.summary.toLowerCase().includes(query)) ||
                        (report.blockchain && report.blockchain.toLowerCase().includes(query))
                    );
                }
                
                this.displayReportList(filteredReports);
                this.updateStatus(`找到 ${filteredReports.length} 个匹配的报告`);
            }
        } catch (error) {
            console.error('搜索失败:', error);
            this.showNotification('搜索失败: ' + error.message, 'error');
        }
    }
    
    // 清空所有报告
    async clearAllReports() {
        if (!confirm('确定要清空所有历史报告吗？此操作不可恢复！')) {
            return;
        }
        
        try {
            const response = await chrome.runtime.sendMessage({ action: 'clearAllReports' });
            
            if (response.success) {
                this.showNotification('所有报告已清空', 'success');
                this.loadReportHistory(); // 重新加载列表
            } else {
                throw new Error(response.error || '清空失败');
            }
        } catch (error) {
            console.error('清空报告失败:', error);
            this.showNotification('清空报告失败: ' + error.message, 'error');
        }
    }
    
    // 加载设置
    async loadSettings() {
        try {
            const result = await chrome.storage.local.get(['settings']);
            if (result.settings) {
                // 应用设置
                console.log('已加载设置:', result.settings);
            }
        } catch (error) {
            console.error('加载设置失败:', error);
        }
    }
    
    // 更新状态
    updateStatus(message) {
        document.getElementById('status').textContent = message;
    }
    
    // 显示通知
    showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // 样式
        notification.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            padding: 12px 16px;
            border-radius: 6px;
            color: white;
            font-size: 13px;
            z-index: 10000;
            max-width: 300px;
            word-wrap: break-word;
            animation: slideIn 0.3s ease;
        `;
        
        // 根据类型设置颜色
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            warning: '#f59e0b',
            info: '#3b82f6'
        };
        
        notification.style.backgroundColor = colors[type] || colors.info;
        
        // 添加到页面
        document.body.appendChild(notification);
        
        // 3秒后自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }
        }, 3000);
    }
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    .report-section {
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .report-section:last-child {
        border-bottom: none;
    }
    
    .report-section h4 {
        color: #1E3A8A;
        margin-bottom: 8px;
        font-size: 14px;
    }
    
    .report-section p {
        color: #374151;
        line-height: 1.6;
        font-size: 13px;
    }
`;
document.head.appendChild(style);

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new AIReportGenerator();
});
