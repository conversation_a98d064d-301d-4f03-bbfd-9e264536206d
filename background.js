// 后台服务工作者
console.log('AI区块链报告生成器后台脚本已启动');

// 安装事件
chrome.runtime.onInstalled.addListener((details) => {
    console.log('插件已安装/更新:', details.reason);
    
    // 初始化存储
    chrome.storage.local.get(['reports', 'settings'], (result) => {
        if (!result.reports) {
            chrome.storage.local.set({ reports: [] });
        }
        if (!result.settings) {
            chrome.storage.local.set({ 
                settings: {
                    apiKey: '',
                    autoFill: true,
                    theme: 'light',
                    language: 'zh-CN'
                }
            });
        }
    });
});

// 处理来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('收到消息:', request);
    
    switch (request.action) {
        case 'getCurrentTab':
            getCurrentTabInfo().then(sendResponse);
            return true; // 保持消息通道开放
            
        case 'generateReport':
            generateAIReport(request.data).then(sendResponse);
            return true;
            
        case 'saveReport':
            saveReport(request.data).then(sendResponse);
            return true;
            
        case 'getReports':
            getReports().then(sendResponse);
            return true;
            
        case 'deleteReport':
            deleteReport(request.reportId).then(sendResponse);
            return true;
            
        case 'clearAllReports':
            clearAllReports().then(sendResponse);
            return true;
            
        default:
            sendResponse({ error: '未知操作' });
    }
});

// 获取当前标签页信息
async function getCurrentTabInfo() {
    try {
        const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
        
        // 注入内容脚本获取页面信息
        const results = await chrome.scripting.executeScript({
            target: { tabId: tab.id },
            function: extractPageInfo
        });
        
        return {
            success: true,
            data: {
                url: tab.url,
                title: tab.title,
                ...results[0].result
            }
        };
    } catch (error) {
        console.error('获取标签页信息失败:', error);
        return { success: false, error: error.message };
    }
}

// 页面信息提取函数（将在内容脚本中执行）
function extractPageInfo() {
    const info = {
        description: '',
        keywords: [],
        contractAddress: '',
        blockchain: ''
    };
    
    // 提取meta描述
    const metaDesc = document.querySelector('meta[name="description"]');
    if (metaDesc) {
        info.description = metaDesc.content;
    }
    
    // 提取关键词
    const metaKeywords = document.querySelector('meta[name="keywords"]');
    if (metaKeywords) {
        info.keywords = metaKeywords.content.split(',').map(k => k.trim());
    }
    
    // 尝试从页面内容中提取合约地址
    const text = document.body.innerText;
    const contractRegex = /0x[a-fA-F0-9]{40}/g;
    const contracts = text.match(contractRegex);
    if (contracts && contracts.length > 0) {
        info.contractAddress = contracts[0];
    }
    
    // 检测区块链网络
    const lowerText = text.toLowerCase();
    if (lowerText.includes('ethereum') || lowerText.includes('eth')) {
        info.blockchain = 'ethereum';
    } else if (lowerText.includes('bsc') || lowerText.includes('binance')) {
        info.blockchain = 'bsc';
    } else if (lowerText.includes('polygon') || lowerText.includes('matic')) {
        info.blockchain = 'polygon';
    }
    
    return info;
}

// 生成AI报告
async function generateAIReport(projectData) {
    try {
        console.log('开始生成AI报告:', projectData);
        
        // 获取API密钥
        const result = await chrome.storage.local.get(['settings']);
        const apiKey = result.settings?.apiKey || 'AIzaSyA3Qpikt4WprxuuRlEws_NKtaTohVxpQCY';
        
        // 构建提示词
        const prompt = buildReportPrompt(projectData);
        
        // 调用Gemini API
        const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=' + apiKey, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.7,
                    topK: 40,
                    topP: 0.95,
                    maxOutputTokens: 4096,
                }
            })
        });
        
        if (!response.ok) {
            throw new Error(`API请求失败: ${response.status}`);
        }
        
        const data = await response.json();
        const reportText = data.candidates[0].content.parts[0].text;
        
        // 解析报告内容
        const report = parseReportContent(reportText, projectData);
        
        return { success: true, data: report };
        
    } catch (error) {
        console.error('生成报告失败:', error);
        
        // 返回模拟数据作为降级策略
        const mockReport = generateMockReport(projectData);
        return { 
            success: true, 
            data: mockReport,
            isMock: true,
            error: error.message 
        };
    }
}

// 构建报告生成提示词
function buildReportPrompt(projectData) {
    return `请为以下区块链项目生成一份专业的分析报告，报告需要包含7个主要章节：

项目信息：
- 项目名称: ${projectData.name}
- 项目网址: ${projectData.url || '未提供'}
- 合约地址: ${projectData.contractAddress || '未提供'}
- 区块链网络: ${projectData.blockchain || '未指定'}
- 项目描述: ${projectData.description || '未提供'}

请按以下结构生成报告：

## 1. 项目概述
[项目的基本信息、核心功能、发展历程]

## 2. 团队背景
[团队成员、技术背景、过往经验]

## 3. 技术架构
[技术实现、智能合约、安全性分析]

## 4. 市场分析
[市场定位、竞争对手、市场前景]

## 5. 代币经济
[代币分配、经济模型、激励机制]

## 6. 风险评估
[技术风险、市场风险、监管风险]

## 7. 投资建议
[综合评分、投资建议、关注要点]

请确保报告内容专业、客观、详细，每个章节都要有具体的分析内容。`;
}

// 解析报告内容
function parseReportContent(reportText, projectData) {
    const sections = reportText.split('## ').filter(section => section.trim());
    
    const report = {
        id: Date.now().toString(),
        projectName: projectData.name,
        projectUrl: projectData.url,
        contractAddress: projectData.contractAddress,
        blockchain: projectData.blockchain,
        createdAt: new Date().toISOString(),
        sections: {},
        fullText: reportText,
        summary: ''
    };
    
    // 解析各个章节
    sections.forEach(section => {
        const lines = section.trim().split('\n');
        const title = lines[0].trim();
        const content = lines.slice(1).join('\n').trim();
        
        if (title.includes('项目概述')) {
            report.sections.overview = content;
            report.summary = content.substring(0, 200) + '...';
        } else if (title.includes('团队背景')) {
            report.sections.team = content;
        } else if (title.includes('技术架构')) {
            report.sections.technology = content;
        } else if (title.includes('市场分析')) {
            report.sections.market = content;
        } else if (title.includes('代币经济')) {
            report.sections.tokenomics = content;
        } else if (title.includes('风险评估')) {
            report.sections.risks = content;
        } else if (title.includes('投资建议')) {
            report.sections.investment = content;
        }
    });
    
    return report;
}

// 生成模拟报告数据
function generateMockReport(projectData) {
    return {
        id: Date.now().toString(),
        projectName: projectData.name,
        projectUrl: projectData.url,
        contractAddress: projectData.contractAddress,
        blockchain: projectData.blockchain,
        createdAt: new Date().toISOString(),
        isMock: true,
        sections: {
            overview: `${projectData.name}是一个基于${projectData.blockchain || '区块链'}技术的创新项目。该项目致力于通过去中心化的方式解决传统行业痛点，为用户提供更加透明、安全、高效的服务体验。项目团队具有丰富的区块链开发经验，技术架构设计合理，具有良好的发展前景。`,
            team: `项目团队由经验丰富的区块链开发者、产品经理和市场专家组成。核心团队成员在相关领域拥有多年从业经验，曾参与多个成功的区块链项目开发。团队具备强大的技术实力和丰富的行业资源，为项目的成功奠定了坚实基础。`,
            technology: `项目采用先进的区块链技术架构，智能合约经过严格的安全审计。技术实现方案成熟稳定，具有良好的可扩展性和安全性。合约代码开源透明，接受社区监督，确保项目的去中心化特性和用户资产安全。`,
            market: `项目所在市场具有巨大的发展潜力，目标用户群体庞大。通过对比分析主要竞争对手，该项目在技术创新、用户体验等方面具有明显优势。随着区块链技术的普及和应用场景的扩大，项目有望获得快速发展。`,
            tokenomics: `项目代币经济模型设计合理，代币分配透明公正。通过多种激励机制鼓励用户参与生态建设，形成良性循环。代币具有明确的使用场景和价值支撑，长期持有价值较高。`,
            risks: `项目面临的主要风险包括：技术风险（智能合约漏洞、网络安全等）、市场风险（竞争加剧、用户接受度等）、监管风险（政策变化、合规要求等）。建议投资者充分了解相关风险，理性投资。`,
            investment: `综合评估，该项目在技术、团队、市场等方面表现良好，具有较高的投资价值。建议关注项目进展，适度配置。投资评级：B+（推荐关注）。风险提示：区块链投资存在较高风险，请谨慎投资。`
        },
        fullText: `# ${projectData.name} 区块链项目分析报告\n\n## 1. 项目概述\n${projectData.name}是一个基于${projectData.blockchain || '区块链'}技术的创新项目...\n\n[注：这是模拟数据，仅供演示使用]`,
        summary: `${projectData.name}是一个基于区块链技术的创新项目，具有良好的发展前景和投资价值...`
    };
}

// 保存报告
async function saveReport(report) {
    try {
        const result = await chrome.storage.local.get(['reports']);
        const reports = result.reports || [];
        
        // 检查是否已存在相同项目的报告
        const existingIndex = reports.findIndex(r => r.projectName === report.projectName);
        
        if (existingIndex >= 0) {
            reports[existingIndex] = report;
        } else {
            reports.unshift(report);
        }
        
        // 限制最多保存50个报告
        if (reports.length > 50) {
            reports.splice(50);
        }
        
        await chrome.storage.local.set({ reports });
        
        return { success: true, message: '报告保存成功' };
    } catch (error) {
        console.error('保存报告失败:', error);
        return { success: false, error: error.message };
    }
}

// 获取所有报告
async function getReports() {
    try {
        const result = await chrome.storage.local.get(['reports']);
        return { success: true, data: result.reports || [] };
    } catch (error) {
        console.error('获取报告失败:', error);
        return { success: false, error: error.message };
    }
}

// 删除报告
async function deleteReport(reportId) {
    try {
        const result = await chrome.storage.local.get(['reports']);
        const reports = result.reports || [];
        
        const filteredReports = reports.filter(r => r.id !== reportId);
        await chrome.storage.local.set({ reports: filteredReports });
        
        return { success: true, message: '报告删除成功' };
    } catch (error) {
        console.error('删除报告失败:', error);
        return { success: false, error: error.message };
    }
}

// 清空所有报告
async function clearAllReports() {
    try {
        await chrome.storage.local.set({ reports: [] });
        return { success: true, message: '所有报告已清空' };
    } catch (error) {
        console.error('清空报告失败:', error);
        return { success: false, error: error.message };
    }
}
