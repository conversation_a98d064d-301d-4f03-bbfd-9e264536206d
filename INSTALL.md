# 安装和测试指南

## 快速安装

### 1. 准备工作
确保您的Chrome浏览器版本在88以上。

### 2. 安装插件

1. **打开Chrome扩展管理页面**
   - 在地址栏输入：`chrome://extensions/`
   - 或通过菜单：更多工具 → 扩展程序

2. **启用开发者模式**
   - 点击页面右上角的"开发者模式"开关

3. **加载插件**
   - 点击"加载已解压的扩展程序"按钮
   - 选择项目根目录（包含manifest.json的文件夹）
   - 点击"选择文件夹"

4. **确认安装成功**
   - 插件应该出现在扩展程序列表中
   - 浏览器工具栏应该显示插件图标 📊

### 3. 测试插件功能

#### 方法一：使用测试页面
1. 在浏览器中打开项目中的 `test-page.html` 文件
2. 点击插件图标打开弹窗
3. 点击"自动填充当前页面"按钮
4. 验证信息是否正确提取

#### 方法二：手动输入测试
1. 点击插件图标
2. 在"项目输入"标签中手动填写：
   - 项目名称：TestCoin
   - 项目网址：https://example.com
   - 合约地址：******************************************
   - 区块链网络：ethereum
3. 点击"验证并继续"

#### 方法三：实际网站测试
访问真实的区块链项目网站，如：
- https://uniswap.org/
- https://compound.finance/
- https://aave.com/

## 功能测试清单

### ✅ 基础功能测试

- [ ] 插件图标正常显示
- [ ] 弹窗界面正常打开
- [ ] 三个标签页可以正常切换
- [ ] 表单输入验证正常工作
- [ ] 自动填充功能正常工作

### ✅ 报告生成测试

- [ ] 模拟数据生成功能正常
- [ ] AI报告生成功能正常（需要网络）
- [ ] 进度条正常显示
- [ ] 报告预览正常显示
- [ ] 7个章节内容完整

### ✅ 存储管理测试

- [ ] 报告保存功能正常
- [ ] 历史记录显示正常
- [ ] 搜索功能正常工作
- [ ] 报告删除功能正常
- [ ] 导出功能正常工作

## 常见问题解决

### 问题1：插件无法加载
**解决方案：**
- 确认选择了正确的文件夹（包含manifest.json）
- 检查manifest.json语法是否正确
- 重启Chrome浏览器后重试

### 问题2：自动填充不工作
**解决方案：**
- 确认当前页面包含区块链项目信息
- 检查页面是否完全加载
- 尝试刷新页面后重试

### 问题3：AI报告生成失败
**解决方案：**
- 检查网络连接
- 使用"模拟数据"功能测试其他功能
- 查看浏览器控制台错误信息

### 问题4：报告无法保存
**解决方案：**
- 检查Chrome存储权限
- 清除浏览器缓存后重试
- 重新安装插件

## 开发者调试

### 1. 查看控制台日志
- 右键点击插件图标 → 检查弹出内容
- 打开开发者工具查看Console输出
- 检查Network标签查看API请求

### 2. 后台脚本调试
- 在扩展管理页面点击"检查视图 service worker"
- 查看后台脚本的控制台输出

### 3. 内容脚本调试
- 在任意网页按F12打开开发者工具
- 在Console中查看内容脚本输出

## 性能优化建议

### 1. 网络优化
- 在稳定网络环境下使用
- 避免频繁调用AI API
- 优先使用模拟数据进行功能测试

### 2. 存储优化
- 定期清理历史报告
- 避免存储过大的报告内容
- 使用搜索功能快速定位报告

### 3. 界面优化
- 关闭不必要的浏览器标签页
- 确保有足够的内存空间
- 使用最新版本的Chrome浏览器

## 卸载插件

如需卸载插件：
1. 打开 `chrome://extensions/`
2. 找到"AI区块链项目报告生成器"
3. 点击"移除"按钮
4. 确认删除

## 技术支持

如遇到技术问题：
1. 查看浏览器控制台错误信息
2. 检查网络连接状态
3. 尝试重新安装插件
4. 联系开发团队获取支持

---

**祝您使用愉快！** 🚀
