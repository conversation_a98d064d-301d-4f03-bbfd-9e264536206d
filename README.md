# AI区块链项目报告生成器

一个基于AI技术的Chrome浏览器插件，能够自动生成专业的区块链项目分析报告。

## 功能特点

### 🚀 核心功能
- **智能项目分析**: 基于Google Gemini AI生成专业报告
- **多格式输入**: 支持网址、项目名称、合约地址等多种输入方式
- **自动信息提取**: 智能识别当前页面的区块链项目信息
- **7大分析维度**: 项目概述、团队背景、技术架构、市场分析、代币经济、风险评估、投资建议

### 📊 报告管理
- **本地存储**: 使用Chrome Storage API安全存储历史报告
- **搜索过滤**: 快速查找历史报告
- **多格式导出**: 支持JSON和PDF格式导出
- **批量管理**: 支持批量删除和清空操作

### 🎨 用户界面
- **响应式设计**: 适配不同屏幕尺寸
- **直观操作**: 标签式导航，操作简单明了
- **实时反馈**: 进度条显示生成过程
- **主题适配**: 专业的蓝色主题设计

## 安装方法

### 开发者模式安装

1. **下载源码**
   ```bash
   git clone [repository-url]
   cd 7.extention-aireport
   ```

2. **打开Chrome扩展管理页面**
   - 在Chrome浏览器中输入 `chrome://extensions/`
   - 或者通过菜单：更多工具 → 扩展程序

3. **启用开发者模式**
   - 点击右上角的"开发者模式"开关

4. **加载插件**
   - 点击"加载已解压的扩展程序"
   - 选择项目文件夹

5. **确认安装**
   - 插件图标应该出现在浏览器工具栏中

## 使用指南

### 1. 项目信息输入
- 点击插件图标打开弹窗
- 在"项目输入"标签中填写项目信息
- 可以使用"自动填充当前页面"功能快速获取信息
- 支持的输入字段：
  - 项目网址
  - 项目名称（必填）
  - 合约地址
  - 项目描述
  - 区块链网络

### 2. 生成报告
- 完成项目信息输入后，切换到"生成报告"标签
- 点击"生成报告"使用AI生成专业报告
- 或点击"使用模拟数据"快速体验功能
- 观察进度条了解生成进度

### 3. 报告管理
- 在"历史记录"标签中查看所有已生成的报告
- 使用搜索功能快速查找特定报告
- 支持查看、导出、删除等操作

## 技术架构

### 前端技术
- **HTML5 + CSS3**: 响应式用户界面
- **原生JavaScript**: 核心逻辑实现
- **Chrome Extension APIs**: 浏览器扩展功能

### 后端集成
- **Google Gemini API**: AI报告生成
- **Chrome Storage API**: 本地数据存储
- **Content Scripts**: 页面信息提取

### 文件结构
```
├── manifest.json          # 插件配置文件
├── popup.html             # 弹窗界面
├── popup.css              # 样式文件
├── background.js          # 后台脚本
├── content.js             # 内容脚本
├── scripts/
│   └── popup.js          # 弹窗逻辑
├── styles/
│   └── popup.css         # 样式文件
├── icons/                # 图标资源
│   ├── icon16.png
│   ├── icon32.png
│   ├── icon48.png
│   └── icon128.png
└── README.md             # 说明文档
```

## API配置

### Gemini API设置
插件默认使用内置的API密钥，如需使用自己的密钥：

1. 获取Google Gemini API密钥
2. 在`background.js`中修改`apiKey`变量
3. 重新加载插件

### 网络要求
- 需要访问`generativelanguage.googleapis.com`
- 支持CORS跨域请求
- 建议在稳定网络环境下使用

## 功能演示

### 自动信息提取
插件能够智能识别当前页面的区块链项目信息：
- 自动提取项目名称和描述
- 识别合约地址
- 检测区块链网络类型
- 提取社交媒体链接

### AI报告生成
基于输入信息生成包含7个章节的专业报告：
1. **项目概述** - 基本信息和核心功能
2. **团队背景** - 团队成员和技术实力
3. **技术架构** - 技术实现和安全性
4. **市场分析** - 市场定位和竞争优势
5. **代币经济** - 经济模型和激励机制
6. **风险评估** - 各类风险分析
7. **投资建议** - 综合评分和建议

### 降级策略
当AI API不可用时，插件会：
- 自动切换到模拟数据模式
- 生成结构完整的示例报告
- 保持所有功能正常运行
- 明确标识为模拟数据

## 注意事项

### 使用限制
- 需要Chrome浏览器版本88+
- 需要网络连接访问AI服务
- API调用可能有频率限制

### 数据安全
- 所有数据本地存储，不上传到服务器
- 支持手动清空所有数据
- 遵循Chrome扩展安全规范

### 免责声明
- 生成的报告仅供参考，不构成投资建议
- 用户应当独立判断投资风险
- 模拟数据仅用于功能演示

## 开发说明

### 本地开发
1. 修改源码后重新加载插件
2. 使用Chrome开发者工具调试
3. 查看控制台输出了解运行状态

### 自定义配置
- 修改`styles/popup.css`自定义界面样式
- 编辑`background.js`调整API配置
- 更新`manifest.json`修改权限设置

## 版本历史

### v1.0.0 (当前版本)
- ✅ 基础插件框架
- ✅ 项目信息输入功能
- ✅ AI报告生成集成
- ✅ 本地存储和管理
- ✅ 响应式用户界面
- ✅ 多格式导出功能

## 支持与反馈

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件反馈
- 参与项目讨论

---

**AI区块链项目报告生成器** - 让区块链项目分析更智能、更专业！
