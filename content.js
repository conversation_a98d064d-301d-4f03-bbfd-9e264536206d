// 内容脚本 - 在网页中运行
console.log('AI区块链报告生成器内容脚本已加载');

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'extractPageInfo') {
        const pageInfo = extractPageInfo();
        sendResponse({ success: true, data: pageInfo });
    }
});

// 提取页面信息
function extractPageInfo() {
    const info = {
        url: window.location.href,
        title: document.title,
        description: '',
        keywords: [],
        contractAddress: '',
        blockchain: '',
        socialLinks: {},
        additionalInfo: {}
    };
    
    // 提取meta信息
    extractMetaInfo(info);
    
    // 提取合约地址
    extractContractAddresses(info);
    
    // 检测区块链网络
    detectBlockchainNetwork(info);
    
    // 提取社交媒体链接
    extractSocialLinks(info);
    
    // 提取其他有用信息
    extractAdditionalInfo(info);
    
    return info;
}

// 提取meta信息
function extractMetaInfo(info) {
    // 描述
    const metaDesc = document.querySelector('meta[name="description"]') || 
                    document.querySelector('meta[property="og:description"]');
    if (metaDesc) {
        info.description = metaDesc.content.trim();
    }
    
    // 关键词
    const metaKeywords = document.querySelector('meta[name="keywords"]');
    if (metaKeywords) {
        info.keywords = metaKeywords.content.split(',').map(k => k.trim()).filter(k => k);
    }
    
    // Open Graph信息
    const ogTitle = document.querySelector('meta[property="og:title"]');
    if (ogTitle && !info.title) {
        info.title = ogTitle.content;
    }
    
    const ogImage = document.querySelector('meta[property="og:image"]');
    if (ogImage) {
        info.additionalInfo.image = ogImage.content;
    }
}

// 提取合约地址
function extractContractAddresses(info) {
    const text = document.body.innerText;
    
    // 以太坊地址格式
    const ethRegex = /0x[a-fA-F0-9]{40}/g;
    const addresses = text.match(ethRegex);
    
    if (addresses && addresses.length > 0) {
        // 去重并取前几个
        const uniqueAddresses = [...new Set(addresses)];
        info.contractAddress = uniqueAddresses[0];
        
        if (uniqueAddresses.length > 1) {
            info.additionalInfo.allAddresses = uniqueAddresses.slice(0, 5);
        }
    }
    
    // 查找页面中明确标注的合约地址
    const contractElements = document.querySelectorAll('[class*="contract"], [class*="address"], [id*="contract"], [id*="address"]');
    contractElements.forEach(element => {
        const elementText = element.innerText;
        const match = elementText.match(ethRegex);
        if (match && !info.contractAddress) {
            info.contractAddress = match[0];
        }
    });
}

// 检测区块链网络
function detectBlockchainNetwork(info) {
    const text = document.body.innerText.toLowerCase();
    const url = window.location.href.toLowerCase();
    
    // 检测网络类型
    if (text.includes('ethereum') || text.includes('eth') || url.includes('ethereum')) {
        info.blockchain = 'ethereum';
    } else if (text.includes('bsc') || text.includes('binance smart chain') || text.includes('bnb')) {
        info.blockchain = 'bsc';
    } else if (text.includes('polygon') || text.includes('matic')) {
        info.blockchain = 'polygon';
    } else if (text.includes('arbitrum')) {
        info.blockchain = 'arbitrum';
    } else if (text.includes('optimism')) {
        info.blockchain = 'optimism';
    } else if (text.includes('avalanche') || text.includes('avax')) {
        info.blockchain = 'avalanche';
    } else if (text.includes('solana') || text.includes('sol')) {
        info.blockchain = 'solana';
    }
    
    // 通过域名检测
    if (url.includes('etherscan.io')) {
        info.blockchain = 'ethereum';
    } else if (url.includes('bscscan.com')) {
        info.blockchain = 'bsc';
    } else if (url.includes('polygonscan.com')) {
        info.blockchain = 'polygon';
    }
}

// 提取社交媒体链接
function extractSocialLinks(info) {
    const socialPlatforms = {
        twitter: ['twitter.com', 't.co'],
        telegram: ['t.me', 'telegram.me'],
        discord: ['discord.gg', 'discord.com'],
        github: ['github.com'],
        medium: ['medium.com'],
        reddit: ['reddit.com'],
        youtube: ['youtube.com', 'youtu.be']
    };
    
    // 查找所有链接
    const links = document.querySelectorAll('a[href]');
    
    links.forEach(link => {
        const href = link.href.toLowerCase();
        
        Object.entries(socialPlatforms).forEach(([platform, domains]) => {
            if (domains.some(domain => href.includes(domain))) {
                if (!info.socialLinks[platform]) {
                    info.socialLinks[platform] = link.href;
                }
            }
        });
    });
}

// 提取其他有用信息
function extractAdditionalInfo(info) {
    const text = document.body.innerText;
    
    // 查找代币符号
    const tokenSymbolRegex = /\$([A-Z]{2,10})\b/g;
    const tokenMatches = text.match(tokenSymbolRegex);
    if (tokenMatches) {
        info.additionalInfo.tokenSymbols = [...new Set(tokenMatches)].slice(0, 5);
    }
    
    // 查找价格信息
    const priceRegex = /\$[\d,]+\.?\d*/g;
    const priceMatches = text.match(priceRegex);
    if (priceMatches) {
        info.additionalInfo.prices = [...new Set(priceMatches)].slice(0, 3);
    }
    
    // 查找市值信息
    if (text.toLowerCase().includes('market cap') || text.toLowerCase().includes('市值')) {
        const marketCapRegex = /market cap[:\s]*\$?[\d,]+\.?\d*[kmb]?/gi;
        const marketCapMatch = text.match(marketCapRegex);
        if (marketCapMatch) {
            info.additionalInfo.marketCap = marketCapMatch[0];
        }
    }
    
    // 查找总供应量
    if (text.toLowerCase().includes('total supply') || text.toLowerCase().includes('总供应量')) {
        const supplyRegex = /total supply[:\s]*[\d,]+\.?\d*[kmb]?/gi;
        const supplyMatch = text.match(supplyRegex);
        if (supplyMatch) {
            info.additionalInfo.totalSupply = supplyMatch[0];
        }
    }
    
    // 检测是否为DeFi项目
    const defiKeywords = ['defi', 'decentralized finance', 'yield farming', 'liquidity', 'staking', 'swap'];
    if (defiKeywords.some(keyword => text.toLowerCase().includes(keyword))) {
        info.additionalInfo.category = 'DeFi';
    }
    
    // 检测是否为NFT项目
    const nftKeywords = ['nft', 'non-fungible token', 'collectible', 'opensea'];
    if (nftKeywords.some(keyword => text.toLowerCase().includes(keyword))) {
        info.additionalInfo.category = 'NFT';
    }
    
    // 检测是否为游戏项目
    const gameKeywords = ['game', 'gaming', 'play to earn', 'p2e', 'metaverse'];
    if (gameKeywords.some(keyword => text.toLowerCase().includes(keyword))) {
        info.additionalInfo.category = 'Gaming';
    }
}

// 创建页面标记（可选功能）
function createPageMarker() {
    // 检查是否已经创建过标记
    if (document.getElementById('ai-report-marker')) {
        return;
    }
    
    // 创建一个小的浮动按钮
    const marker = document.createElement('div');
    marker.id = 'ai-report-marker';
    marker.innerHTML = '📊';
    marker.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        width: 40px;
        height: 40px;
        background: #1E3A8A;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 10000;
        font-size: 18px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        transition: all 0.3s ease;
    `;
    
    marker.addEventListener('mouseenter', () => {
        marker.style.transform = 'scale(1.1)';
    });
    
    marker.addEventListener('mouseleave', () => {
        marker.style.transform = 'scale(1)';
    });
    
    marker.addEventListener('click', () => {
        // 发送消息给background script打开popup
        chrome.runtime.sendMessage({ action: 'openPopup' });
    });
    
    document.body.appendChild(marker);
    
    // 3秒后自动隐藏
    setTimeout(() => {
        if (marker.parentNode) {
            marker.style.opacity = '0.3';
        }
    }, 3000);
}

// 页面加载完成后执行
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(createPageMarker, 1000);
    });
} else {
    setTimeout(createPageMarker, 1000);
}

// 导出函数供其他脚本使用
window.aiReportExtractor = {
    extractPageInfo,
    extractMetaInfo,
    extractContractAddresses,
    detectBlockchainNetwork,
    extractSocialLinks,
    extractAdditionalInfo
};
