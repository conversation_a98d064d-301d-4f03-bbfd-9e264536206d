<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="这是一个测试区块链项目，用于演示AI报告生成插件的功能">
    <meta name="keywords" content="区块链,<PERSON><PERSON><PERSON>,以太坊,智能合约">
    <title>测试区块链项目 - TestCoin</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #1E3A8A, #3B82F6);
            color: white;
            padding: 40px 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            background: white;
            padding: 30px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .contract-address {
            background: #f0f9ff;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 14px;
            border: 1px solid #0ea5e9;
            margin: 10px 0;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-item {
            background: #f8fafc;
            padding: 20px;
            border-radius: 6px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #1E3A8A;
        }
        .social-links {
            display: flex;
            gap: 15px;
            margin: 20px 0;
        }
        .social-links a {
            padding: 10px 20px;
            background: #1E3A8A;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: background 0.3s;
        }
        .social-links a:hover {
            background: #1e40af;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>TestCoin (TEST)</h1>
        <p>下一代去中心化金融协议</p>
        <p>基于以太坊的创新DeFi项目</p>
    </div>

    <div class="section">
        <h2>项目概述</h2>
        <p>TestCoin是一个基于以太坊区块链的去中心化金融(DeFi)协议，致力于为用户提供安全、高效、透明的金融服务。我们的目标是通过智能合约技术，构建一个无需信任的金融生态系统。</p>
        
        <h3>核心功能</h3>
        <ul>
            <li>流动性挖矿和收益农场</li>
            <li>去中心化交易所(DEX)</li>
            <li>借贷协议</li>
            <li>治理代币投票</li>
        </ul>
    </div>

    <div class="section">
        <h2>智能合约信息</h2>
        <p><strong>主合约地址:</strong></p>
        <div class="contract-address">
            ******************************************
        </div>
        
        <p><strong>网络:</strong> Ethereum Mainnet</p>
        <p><strong>合约验证:</strong> ✅ 已验证</p>
        <p><strong>安全审计:</strong> ✅ 已通过多家机构审计</p>
    </div>

    <div class="section">
        <h2>代币信息</h2>
        <div class="stats">
            <div class="stat-item">
                <div class="stat-value">$TEST</div>
                <div>代币符号</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">$2.45</div>
                <div>当前价格</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">$24.5M</div>
                <div>市值</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">10M</div>
                <div>总供应量</div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>团队背景</h2>
        <p>我们的团队由来自顶级科技公司和金融机构的专业人士组成，在区块链、金融科技和软件开发领域拥有丰富经验。团队成员曾参与多个成功的DeFi项目开发。</p>
        
        <h3>核心团队</h3>
        <ul>
            <li><strong>张三</strong> - 创始人兼CEO，前以太坊基金会成员</li>
            <li><strong>李四</strong> - 技术总监，区块链安全专家</li>
            <li><strong>王五</strong> - 产品总监，DeFi产品设计专家</li>
        </ul>
    </div>

    <div class="section">
        <h2>路线图</h2>
        <ul>
            <li><strong>Q1 2024:</strong> 主网上线，基础功能发布</li>
            <li><strong>Q2 2024:</strong> 流动性挖矿启动</li>
            <li><strong>Q3 2024:</strong> 治理功能上线</li>
            <li><strong>Q4 2024:</strong> 跨链桥接功能</li>
        </ul>
    </div>

    <div class="section">
        <h2>社交媒体</h2>
        <div class="social-links">
            <a href="https://twitter.com/testcoin" target="_blank">Twitter</a>
            <a href="https://t.me/testcoin" target="_blank">Telegram</a>
            <a href="https://discord.gg/testcoin" target="_blank">Discord</a>
            <a href="https://github.com/testcoin" target="_blank">GitHub</a>
            <a href="https://medium.com/@testcoin" target="_blank">Medium</a>
        </div>
    </div>

    <div class="section">
        <h2>风险提示</h2>
        <p><strong>投资有风险，入市需谨慎。</strong></p>
        <p>DeFi项目存在智能合约风险、市场风险、技术风险等多种风险。请在充分了解项目和风险的基础上做出投资决策。本项目信息仅供参考，不构成投资建议。</p>
    </div>

    <script>
        // 模拟一些动态内容
        document.addEventListener('DOMContentLoaded', function() {
            console.log('TestCoin项目页面已加载');
            
            // 模拟价格更新
            setInterval(function() {
                const priceElement = document.querySelector('.stat-value');
                if (priceElement && priceElement.textContent.includes('$')) {
                    const currentPrice = parseFloat(priceElement.textContent.replace('$', ''));
                    const change = (Math.random() - 0.5) * 0.1;
                    const newPrice = (currentPrice + change).toFixed(2);
                    priceElement.textContent = '$' + newPrice;
                }
            }, 5000);
        });
    </script>
</body>
</html>
